import { Hero<PERSON>ni<PERSON>, LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { resHelper } from "../../../../common/helper/ResHelper";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import RoleSpeakCmpt from "../../role/RoleSpeakCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetEntry1001Cmpt extends PlanetEntryCmpt {

    private _regUpdateEvt: { fn: (dt: number) => Promise<void>, interval: number, lastTime?: number }[] = []

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onFunctionUnlock },
        ]
    }

    private showPnls: string[] = []

    public async preload(model: PlanetModel) {
        super.preload(model)
        if (mc.preWindName == "planetQuestion") {
            this.showPnls.push("planetQuestion/PlanetQuestionSelectPnl")
        }
        if (this.showPnls.length > 0) {
            await ut.promiseMap(this.showPnls, async (pnl) => {
                await viewHelper.preloadPnl(pnl)
            })
        }
    }

    protected initView() {
        this.initShowPnls()
        this.initProfileBranch()
        this.initCollect()

        this.initFixed1()
        this.initFixed2()
    }

    private initShowPnls() {
        for (let pnlName of this.showPnls) {
            viewHelper.showPnl(pnlName)
        }
    }

    private initProfileBranch() {
        let node = this.Component(MountPointCmpt).getPoint("profileBranch")
        let type = UIFunctionType.PLAY_ARCHIVES

        let role = this.getDoctor()
        let speakCmpt = role.Component(RoleSpeakCmpt)
        let npcId = 1001
        speakCmpt.init(npcId)

        node.Child("scale/play_name", PlanetPlayNameCmpt).init(this.model, type)
        let touch = node.getChildByName("touch")
        touch.on("click", () => {
            // let key = cfgHelper.getPlotId("guide_bubble_1001_1")
            // let node = speakCmpt.getTalkNode()
            // if (node) {
            //     viewHelper.showSpeechBubble(node.convertToWorldSpaceAR(cc.v2(0, 0)), key)
            // }
            viewHelper.showPnl("planetQuestion/PlanetQuestionSelectPnl")
        })
    }

    private initCollect() {
        let type = UIFunctionType.PLAY_COLLECT
        let collect = this.Component(MountPointCmpt).getPoint("collect")
        collect.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        collect.active = unlockHelper.isUnlockFunction(UIFunctionType.PLAY_DAILY_TASK)
        collect.off("click")
        collect.on("click", () => {
            viewHelper.gotoWind("collect")
        })
    }

    private getProfileBranch() {
        return this.Component(MountPointCmpt).getPoint("profileBranch")
    }

    private getDoctor() {
        return this.getProfileBranch().getChildByName("sk")
    }

    private onFunctionUnlock(type: UIFunctionType) {
    }

    update(dt: number) {
        for (const evt of this._regUpdateEvt) {
            const { fn, interval, lastTime } = evt
            let next = !lastTime
            evt.lastTime -= dt * 1000
            if (next || evt.lastTime <= 0) {
                fn(dt)
                evt.lastTime = interval
            }
        }
    }

    private reg(fn: (dt: number) => Promise<void>, interval: number) {
        this._regUpdateEvt.push({ fn, interval })
    }

    private initFixed1() {
        const landspace = this.Component(MountPointCmpt).getPoint("landspace")
        const tmp = landspace.Child("unit")

        const l1 = landspace.Child("1")
        const left = cc.instantiate2(tmp, l1)
        const right = cc.instantiate2(tmp, l1)
        left.active = right.active = true
        left.Child("body").scaleX = -1
        const lyt = l1.Component(cc.Layout)
        lyt.updateLayout()
        lyt.enabled = false
        left.Child("body").Component(sp.Skeleton).playAnimation("idle_2", true)
        right.Child("body").Component(sp.Skeleton).playAnimation("idle_1", true)

        left.setSiblingIndex(1)
        right.setSiblingIndex(0)

        this.reg(async (dt: number) => {
            const sk = right.Child("body").Component(sp.Skeleton)
            const track = sk.getCurrent(0)
            if (track.animation.name == "idle_3") return
            await sk.playAnimation("idle_3", false)
            sk.playAnimation("idle_1", true)
        }, 10000)

        this.bindPeopleEvt(left)
        this.bindPeopleEvt(right)
    }
    private bindPeopleEvt(node: cc.Node) {
        node.Child("dialog").active = false
        node.off("click")
        node.on("click", () => {
            const dialog = node.Child("dialog")
            if (dialog.active) return
            if (ut.random(0, 100) > 20) return
            dialog.Child("lbl", cc.Label).string = "%^$&"
            dialog.active = true
            this.scheduleOnce(() => {
                dialog.active = false
            }, 2)
        })
    }

    private initFixed2() {
        const people1 = this.Component(MountPointCmpt).getPoint("people1")
        this.bindPeopleEvt(people1)
    }

}