import CoreEventType from "../../../../../core/event/CoreEventType";
import { util } from "../../../../../core/utils/Utils";
import { HeroAnimation, LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, PLANET_ITEM_ID, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import NodeType from "../../../../common/event/NodeType";
import { cfgHelper } from "../../../../common/helper/CfgHelper";
import { gameHelper } from "../../../../common/helper/GameHelper";
import { resHelper } from "../../../../common/helper/ResHelper";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import UnlockBlackHoleCmpt from "../eternalGarden/UnlockBlackHoleCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;

const PLOT_KEY = "guidePlot_key_4002_1"

@ccclass
export default class PlanetEntry1005Cmpt extends PlanetEntryCmpt {

    private flyBird: cc.Node = null
    private right: boolean = true

    public listenEventMaps() {
        return [
            { [EventType.END_PLOT]: this.onPlotEnd },
            { [CoreEventType.CLOSE_PNL]: this.onClosePnl, tag: "create" },
            { [EventType.USE_PLANET_ITEM]: this.onUsePlanetItem },
            { [EventType.CHECK_USE_PLANET_ITEM]: this.checkUsePlanetItem },
        ]
    }

    private shopWaitAppear: boolean = false

    protected initView() {
        this.initBlackHole()
        this.initShop()

        this.playFlyBird()
    }

    private initBlackHole() {
        let type = UIFunctionType.PLAY_BLACKHOLE
        let blackHole = this.Component(MountPointCmpt).getPoint("blackHole")
        blackHole.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        let unlock = unlockHelper.isUnlockFunction(type)
        blackHole.Child("touch").off("click")
        blackHole.Child("touch").on("click", () => {
            if (!unlock) {
                viewHelper.showAlert("unlockFunc_tips_7")
                return
            }
            if (gameHelper.blackHole.isStart()) {
                if (gameHelper.blackHole.isRoundEnd()) {
                    viewHelper.showAlert("blackHole_tips_3")
                }
                else {
                    viewHelper.showPnl("blackHole/BlackHolePnl")
                }
            }
            else {
                viewHelper.showPnl("blackHole/BlackHoleReadyPnl")
            }
        })
        blackHole.Child("lock").active = !unlock
        blackHole.Child("unlock").active = unlock
    }

    private initShop() {
        let shop = this.Component(MountPointCmpt).getPoint(NPC_ID.BLACK_HOLE_SHOP)
        let unlock = unlockHelper.isUnlockFunction(UIFunctionType.PLAY_BLACKHOLE)
        shop.active = unlock && !this.shopWaitAppear
        let name = shop.Child("play_name")
        name.Component(PlanetPlayNameCmpt).init(this.model, UIFunctionType.BLACK_HOLE_SHOP)

        let reddot = shop.Child("reddot")
        reddot.active = gameHelper.new.isNew(MarkNewType.NPC_DIALOG, [NPC_ID.BLACK_HOLE_SHOP])
        name.active = !reddot.active

        shop.off("click")
        shop.on("click", () => {
            if (gameHelper.new.isNew(MarkNewType.NPC_DIALOG, [NPC_ID.BLACK_HOLE_SHOP])) {
                gameHelper.plot.start(PLOT_KEY)
                gameHelper.new.removeNew(MarkNewType.NPC_DIALOG, [NPC_ID.BLACK_HOLE_SHOP])
                reddot.active = false
                name.active = true
            }
            else {
                if (this.checkClickTaskDialogNpc(shop)) return
                this.showStore()
            }
        })
    }

    private async showUnlockBlackHole() {
        viewHelper.showUI(false)
        let pnl = "blackHole/BlackHoleReadyPnl"
        let node = await resHelper.loadPrefabByUrl("planet/1005/unlock_black_hole", this.node, this.getTag())
        if (!cc.isValid(this)) return
        let p = viewHelper.preloadPnl(pnl)

        node.Component(UnlockBlackHoleCmpt).init()

        await eventCenter.wait(EventType.GUIDE_END_UNLOCK_BLACK_HOLE)

        await p
        viewHelper.showPnl(pnl)
        viewHelper.showUI(true)
        this.shopWaitAppear = true
        this.initView()
        node.destroy()
    }

    private onPlotEnd(plotkey: string) {
        if (plotkey != PLOT_KEY) return
        this.showStore()
    }

    private showStore() {
        viewHelper.showPnl("blackHole/BlackHoleStore")
    }

    public getNpc() {
        return this.getShop().getChildByName("sk")
    }

    private onClosePnl(pnl: mc.BasePnlCtrl) {
        if (!this.shopWaitAppear) return
        let key = pnl.key
        let key1 = "blackHole/BlackHolePnl"
        let key2 = "blackHole/BlackHoleReadyPnl"
        if (key != key1 && key != key2) return
        if (!viewHelper.checkPnlClose(key1) || !viewHelper.checkPnlClose(key2)) return
        this.showNpc()
        this.shopWaitAppear = false
    }

    private async showNpc() {
        viewHelper.showUI(false)
        mc.lockTouch(true)
        let shop = this.getShop()
        shop.active = true
        let name = shop.getChildByName("play_name")
        name.active = false
        let reddot = shop.Child("reddot")
        reddot.active = false
        let npc = this.getNpc()
        let sk = npc.Component(sp.Skeleton)
        sk.playAnimation("chuxian").then(() => {
            sk.playAnimation("daiji", true)
        })
        await ut.wait(2.5, this)
        reddot.active = true
        mc.lockTouch(false)
        viewHelper.showUI(true)
    }

    private checkUsePlanetItem(id: string, screenPos: cc.Vec2) {
        if (id != PLANET_ITEM_ID.BLACK_HOLE_KEY) return
        let blackHole = this.Component(MountPointCmpt).getPoint("blackHole")
        let node = blackHole.getChildByName("touch")
        return node._hitTest(screenPos)
    }

    private async onUsePlanetItem(id: string, screenPos: cc.Vec2) {
        if (!this.checkUsePlanetItem(id, screenPos)) return
        gameHelper.blackHole.isUnlock = true
        eventCenter.emit(EventType.REMOVE_PLANET_ITEM, id)

        let succ = await gameHelper.blackHole.unlock()
        if (succ) {
            this.showUnlockBlackHole()
        }
        else {
            gameHelper.blackHole.isUnlock = false
            eventCenter.emit(EventType.ADD_PLANET_ITEM, id)
        }
    }

    private getShop() {
        return this.Component(MountPointCmpt).getPoint(NPC_ID.BLACK_HOLE_SHOP)
    }


    update(dt: number) { }

    private async playFlyBird() {
        const path = this.Component(MountPointCmpt).getPoint("fly_path")
        if (!this.flyBird) {
            this.flyBird = this.Component(MountPointCmpt).getPoint("fly_bird")
        }
        this.startBirdFlyLoop(path)
    }

    private async startBirdFlyLoop(path: cc.Node) {
        while (cc.isValid(this) && cc.isValid(this.flyBird)) {
            const waitTime = ut.randomRange(2, 5)
            await ut.wait(waitTime, this)
            if (!cc.isValid(this)) break
            await this.performBirdFlight(path)
        }
    }

    private async performBirdFlight(path: cc.Node) {
        const inNodes = path.Child("in").children
        const outNodes = path.Child("out").children
        const inNode = this.right ? inNodes.random() : outNodes.random()
        const outNode = this.right ? outNodes.random() : inNodes.random()
        
        this.right = !this.right
        this.flyBird.setPosition(inNode.position)
        this.flyBird.active = true
        const sk = this.flyBird.Child("body", sp.Skeleton)
        sk.playAnimation("fly", true)
        await this.createRealisticBirdFlight(this.flyBird, cc.v2(inNode.position.x, inNode.position.y), cc.v2(outNode.position.x, outNode.position.y))
        this.flyBird.active = false
        sk.clearTracks()
    }

    private async createRealisticBirdFlight(bird: cc.Node, startPos: cc.Vec2, endPos: cc.Vec2): Promise<void> {
        return new Promise((resolve) => {
            const deltaX = endPos.x - startPos.x
            const deltaY = endPos.y - startPos.y
            const horizontalDistance = Math.abs(deltaX)
            const totalDistance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

            // 飞行时间基于距离，但加入随机性 (2-6秒)
            const baseTime = Math.max(2, Math.min(6, totalDistance / 150))
            const flyTime = baseTime * ut.randomRange(0.8, 1.2)
            // 获取屏幕边界限制
            const screenHeight = cc.winSize.height
            const maxFlightHeight = screenHeight * 0.8 // 限制在屏幕高度的80%以内
            const minFlightHeight = -screenHeight * 0.2 // 允许稍微飞出屏幕下方

            let controlY1: number, controlY2: number

            if (deltaY > 50) {
                // 向上飞行：需要更大的弧度来克服重力，但限制最大高度
                const baseHeight1 = startPos.y + horizontalDistance * 0.2 + deltaY * 0.15
                const baseHeight2 = startPos.y + horizontalDistance * 0.3 + deltaY * 0.4

                controlY1 = Math.min(baseHeight1, maxFlightHeight)
                controlY2 = Math.min(baseHeight2, maxFlightHeight)
            } else if (deltaY < -50) {
                // 向下飞行：先稍微上升然后下降，模拟自然滑翔
                const maxHeight = Math.max(startPos.y, endPos.y) + horizontalDistance * 0.2
                controlY1 = Math.min(maxHeight, maxFlightHeight)
                controlY2 = Math.min(maxHeight * 0.7 + endPos.y * 0.3, maxFlightHeight)
            } else {
                // 水平飞行：轻微的波浪形轨迹
                const waveHeight = horizontalDistance * 0.12
                const baseHeight = Math.max(startPos.y, endPos.y) + waveHeight
                controlY1 = Math.min(baseHeight, maxFlightHeight)
                controlY2 = Math.min(baseHeight * 0.5, maxFlightHeight)
            }

            // 确保控制点不会太低
            controlY1 = Math.max(controlY1, minFlightHeight)
            controlY2 = Math.max(controlY2, minFlightHeight)

            // 创建控制点，添加随机性让每次飞行都不同，但确保在安全范围内
            const control1Y = Math.min(Math.max(controlY1 + ut.randomRange(-15, 15), minFlightHeight), maxFlightHeight)
            const control2Y = Math.min(Math.max(controlY2 + ut.randomRange(-15, 15), minFlightHeight), maxFlightHeight)

            const control1 = cc.v2(
                startPos.x + deltaX * 0.3 + ut.randomRange(-30, 30),
                control1Y
            )
            const control2 = cc.v2(
                startPos.x + deltaX * 0.7 + ut.randomRange(-30, 30),
                control2Y
            )

            this.addBirdFlightDetails(bird, flyTime)
            // 执行贝塞尔曲线飞行，使用合适的缓动函数
            cc.tween(bird)
                .bezierTo(flyTime, control1, control2, endPos)
                .call(() => {
                    resolve()
                })
                .start()
        })
    }

    private addBirdFlightDetails(bird: cc.Node, flyTime: number) {
        // 飞行过程中的轻微缩放变化，模拟远近效果
        const scaleVariation = 0.05
        cc.tween(bird)
            .to(flyTime * 0.3, {
                scale: 1 + scaleVariation,
                angle: ut.randomRange(-3, 3) // 轻微的倾斜
            }, { easing: 'sineOut' })
            .to(flyTime * 0.4, {
                scale: 1 - scaleVariation * 0.5,
                angle: ut.randomRange(-2, 2)
            }, { easing: 'sineInOut' })
            .to(flyTime * 0.3, {
                scale: 1,
                angle: 0
            }, { easing: 'sineIn' })
            .start()

        // 添加轻微的上下波动，模拟扇翅膀的效果
        cc.tween(bird)
            .by(flyTime * 0.25, { y: 8 }, { easing: 'sineOut' })
            .by(flyTime * 0.25, { y: -12 }, { easing: 'sineInOut' })
            .by(flyTime * 0.25, { y: 8 }, { easing: 'sineInOut' })
            .by(flyTime * 0.25, { y: -4 }, { easing: 'sineIn' })
            .start()
    }
}